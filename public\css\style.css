 body { background:#111; color:#fff; font-family:Arial; padding:20px; }
    h1 { margin-bottom:10px; }
    .subtitle { margin-bottom:20px; color:#aaa; }
    .coins-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    .coin-card {
      background: #1a1a1a;
      border: 1px solid #333;
      border-radius: 8px;
      padding: 12px 16px;
      display: flex;
      flex-direction: column;
      gap: 6px;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      min-width: 200px;
    }
    .coin-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.5);
    }
    .coin-card a { color:#ffffff; font-weight:bold; text-decoration:none; font-size: 1.1em; }
    .change { font-weight:bold; }
    .green { color:#00ff00; }
    .spike {
      color: orange;
      font-size: 0.85em;
      font-weight: bold;
      background: rgba(255, 165, 0, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
      border: 1px solid rgba(255, 165, 0, 0.3);
    }
    .rsi {
      font-size: 0.9em;
      font-weight: bold;
      padding: 2px 6px;
      border-radius: 4px;
      border: 1px solid;
    }
    .rsi.oversold {
      color: #ff4444;
      background: rgba(255, 68, 68, 0.1);
      border-color: rgba(255, 68, 68, 0.3);
    }
    .rsi.overbought {
      color: #44ff44;
      background: rgba(68, 255, 68, 0.1);
      border-color: rgba(68, 255, 68, 0.3);
    }
    .rsi.neutral {
      color: #ffaa44;
      background: rgba(255, 170, 68, 0.1);
      border-color: rgba(255, 170, 68, 0.3);
    }
    .price {
      color: #aaa;
      font-size: 0.85em;
    }
    .volume-container {
      margin-top: 6px;
    }
    .volume-label {
      font-size: 0.75em;
      color: #888;
      margin-bottom: 2px;
    }
    .volume-bar {
      height: 6px;
      background: #333;
      border-radius: 3px;
      overflow: hidden;
      position: relative;
    }
    .volume-fill {
      height: 100%;
      border-radius: 3px;
      transition: width 0.5s ease;
      position: relative;
    }
    .volume-fill.normal {
      background: linear-gradient(90deg, #4a9eff, #00ff88);
    }
    .volume-fill.spike {
      background: linear-gradient(90deg, #ff6b35, #f7931e);
      animation: pulse 1.5s infinite;
    }
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }
    .volume-text {
      font-size: 0.7em;
      color: #aaa;
      margin-top: 2px;
    }

    /* Configuration Panel Styles */
    .config-panel {
      background: #1a1a1a;
      border: 1px solid #333;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      align-items: end;
    }
    .config-group {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
    .config-group label {
      font-size: 0.9em;
      color: #ccc;
      font-weight: bold;
      display: flex;
      align-items: center;
      gap: 6px;
    }
    .config-group select,
    .config-group input {
      background: #333;
      border: 1px solid #555;
      border-radius: 4px;
      color: #fff;
      padding: 8px 12px;
      font-size: 0.9em;
      min-width: 100px;
    }
    .config-group select:focus,
    .config-group input:focus {
      outline: none;
      border-color: #00ff00;
    }
    .apply-btn {
      background: linear-gradient(135deg, #00ff00, #00cc00);
      border: none;
      border-radius: 6px;
      color: #000;
      padding: 10px 20px;
      font-weight: bold;
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    .apply-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 255, 0, 0.3);
    }
    .apply-btn:active {
      transform: translateY(0);
    }
    .config-status {
      font-size: 0.85em;
      color: #888;
      margin-left: auto;
      align-self: center;
    }

    /* Info Icon and Tooltip Styles */
    .info-icon {
      cursor: help;
      font-size: 0.8em;
      color: #888;
      transition: color 0.2s ease;
      position: relative;
    }
    .info-icon svg{
        width: 10px;
    }
    .info-icon:hover {
      color: #00ff00;
    }
    .info-icon::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 120%;
        left: 150px;
        transform: translateX(-50%);
        background: #2a2a2a;
        color: #fff;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.95em;
        white-space: nowrap;
        max-width: 300px;
        white-space: normal;
        width: max-content;
        max-width: 280px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        border: 1px solid #444;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
        z-index: 1000;
        font-weight: normal;
        line-height: 1.3;
    }
    .info-icon::before {
    content: '';
    position: absolute;
    bottom: 65%;
    left: 4px;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #444;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 1001;
}
    .info-icon:hover::after,
    .info-icon:hover::before {
      opacity: 1;
      visibility: visible;
    }