const express = require("express");
const http = require("http");
const WebSocket = require("ws");
const { Server } = require("socket.io");
const axios = require("axios");

const BINANCE_API = "https://api.binance.com/api/v3/exchangeInfo";
const STREAM_URL = "wss://stream.binance.com:9443/stream";

// Default configuration values
const DEFAULT_CONFIG = {
  interval: "5m",
  threshold: 0.5,
  volumeMultiplier: 3,
  rsiPeriod: 14
};

const QUOTE_ASSET = "USDT";

// Store user configurations per socket
const userConfigs = new Map(); // socketId -> config object

// Store active WebSocket connections per interval
const intervalStreams = new Map(); // interval -> WebSocket connection

// Cache for kline data to avoid repeated API calls
const klineCache = new Map(); // symbol_interval -> {data, timestamp}

const app = express();
const server = http.createServer(app);
const io = new Server(server);
app.use(express.static("public"));

// ✅ Binance se trading pairs fetch
async function getSymbols(interval = DEFAULT_CONFIG.interval) {
  const res = await fetch(BINANCE_API);
  const data = await res.json();
  return data.symbols
    .filter(
      (s) =>
        s.status === "TRADING" &&
        s.isSpotTradingAllowed &&
        s.symbol.endsWith(QUOTE_ASSET)
    )
    .map((s) => s.symbol.toLowerCase() + "@kline_" + interval);
}

// ✅ Get all unique intervals from connected users
function getActiveIntervals() {
  const intervals = new Set();
  for (const config of userConfigs.values()) {
    intervals.add(config.interval);
  }
  return Array.from(intervals);
}

// ✅ RSI calculation function
function calculateRSI(prices, period = DEFAULT_CONFIG.rsiPeriod) {
  if (prices.length < period + 1) return null;

  const gains = [];
  const losses = [];

  // Calculate price changes
  for (let i = 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? Math.abs(change) : 0);
  }

  // Calculate initial average gain and loss
  let avgGain = gains.slice(0, period).reduce((a, b) => a + b, 0) / period;
  let avgLoss = losses.slice(0, period).reduce((a, b) => a + b, 0) / period;

  // Calculate RSI using Wilder's smoothing method
  for (let i = period; i < gains.length; i++) {
    avgGain = (avgGain * (period - 1) + gains[i]) / period;
    avgLoss = (avgLoss * (period - 1) + losses[i]) / period;
  }

  if (avgLoss === 0) return 100;
  const rs = avgGain / avgLoss;
  const rsi = 100 - (100 / (1 + rs));

  return Math.round(rsi * 100) / 100; // Round to 2 decimal places
}

// ✅ Get cached or fetch kline data
async function getKlineData(symbol, interval, limit = 40) {
  const cacheKey = `${symbol}_${interval}`;
  const now = Date.now();

  // Check cache (valid for 30 seconds)
  if (klineCache.has(cacheKey)) {
    const cached = klineCache.get(cacheKey);
    if (now - cached.timestamp < 30000) {
      return cached.data;
    }
  }

  try {
    const url = `https://api.binance.com/api/v3/klines?symbol=${symbol}&interval=${interval}&limit=${limit}`;
    const res = await axios.get(url);
    const candles = res.data;

    // Cache the data
    klineCache.set(cacheKey, {
      data: candles,
      timestamp: now
    });

    return candles;
  } catch (err) {
    console.error("Kline data error", symbol, interval, err.message);
    return null;
  }
}

// ✅ Get RSI for a symbol with specific config
async function getRSI(symbol, config = DEFAULT_CONFIG) {
  try {
    const candles = await getKlineData(symbol, config.interval, config.rsiPeriod + 20);
    if (!candles) return null;

    // Extract closing prices
    const closePrices = candles.map(candle => parseFloat(candle[4]));

    return calculateRSI(closePrices, config.rsiPeriod);
  } catch (err) {
    console.error("RSI error", symbol, err.message);
    return null;
  }
}

// ✅ volume spike check with detailed volume data
async function checkVolumeSpike(symbol, config = DEFAULT_CONFIG) {
  try {
    const candles = await getKlineData(symbol, config.interval, 20);
    if (!candles) {
      return {
        isSpike: false,
        currentVolume: 0,
        avgVolume: 0,
        maxVolume: 0,
        volumeRatio: 0
      };
    }

    const lastCandle = candles[candles.length - 1];
    const currentVolume = parseFloat(lastCandle[5]);

    const prevVolumes = candles.slice(0, -1).map((c) => parseFloat(c[5]));
    const avgVolume =
      prevVolumes.reduce((a, b) => a + b, 0) / prevVolumes.length;

    const maxVolume = Math.max(...prevVolumes);
    const volumeRatio = currentVolume / avgVolume;

    return {
      isSpike: currentVolume >= config.volumeMultiplier * avgVolume,
      currentVolume,
      avgVolume,
      maxVolume,
      volumeRatio: Math.round(volumeRatio * 100) / 100
    };
  } catch (err) {
    console.error("Volume error", symbol, err.message);
    return {
      isSpike: false,
      currentVolume: 0,
      avgVolume: 0,
      maxVolume: 0,
      volumeRatio: 0
    };
  }
}

function chunkArray(arr, size) {
  const chunks = [];
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  return chunks;
}

function connectStreams(streams, interval) {
  const url = `${STREAM_URL}?streams=${streams.join("/")}`;
  const ws = new WebSocket(url);

  ws.on("open", () => console.log(`Connected ${streams.length} pairs for ${interval}`));

  ws.on("message", async (msg) => {
    const json = JSON.parse(msg);
    const k = json.data.k;

    if (k) {
      const symbol = json.data.s;
      const open = parseFloat(k.o);
      const close = parseFloat(k.c);
      const change = ((close - open) / open) * 100;

      // Process for each connected user with matching interval
      for (const [socketId, config] of userConfigs.entries()) {
        if (config.interval === interval && change >= config.threshold) {
          const socket = io.sockets.sockets.get(socketId);
          if (socket) {
            // ✅ check volume spike and get RSI for this user's config
            const [volumeData, rsi] = await Promise.all([
              checkVolumeSpike(symbol, config),
              getRSI(symbol, config)
            ]);

            socket.emit("green", {
              symbol,
              change: change.toFixed(2),
              spike: volumeData.isSpike,
              rsi,
              volume: parseFloat(k.v), // Current volume from stream
              price: close.toFixed(8),
              volumeData: {
                current: volumeData.currentVolume,
                average: volumeData.avgVolume,
                ratio: volumeData.volumeRatio,
                max: volumeData.maxVolume
              }
            });
          }
        } else if (config.interval === interval && change < config.threshold) {
          const socket = io.sockets.sockets.get(socketId);
          if (socket) {
            socket.emit("red", { symbol });
          }
        }
      }
    }
  });

  ws.on("close", () => {
    console.log(`${interval} stream closed. Reconnecting...`);
    setTimeout(() => {
      if (getActiveIntervals().includes(interval)) {
        connectStreams(streams, interval);
      }
    }, 3000);
  });

  return ws;
}

// ✅ Start streams for a specific interval
async function startIntervalStream(interval) {
  if (intervalStreams.has(interval)) {
    return; // Already connected
  }

  const symbols = await getSymbols(interval);
  console.log(`Starting ${interval} stream for ${symbols.length} USDT pairs`);
  const chunks = chunkArray(symbols, 200);

  const connections = chunks.map(chunk => connectStreams(chunk, interval));
  intervalStreams.set(interval, connections);
}

// ✅ Stop streams for a specific interval
function stopIntervalStream(interval) {
  if (intervalStreams.has(interval)) {
    const connections = intervalStreams.get(interval);
    connections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    });
    intervalStreams.delete(interval);
    console.log(`Stopped ${interval} stream`);
  }
}

// ✅ Update active streams based on user configurations
async function updateActiveStreams() {
  const activeIntervals = getActiveIntervals();

  // Start new intervals
  for (const interval of activeIntervals) {
    if (!intervalStreams.has(interval)) {
      await startIntervalStream(interval);
    }
  }

  // Stop unused intervals
  for (const interval of intervalStreams.keys()) {
    if (!activeIntervals.includes(interval)) {
      stopIntervalStream(interval);
    }
  }
}

(async () => {
  // Start with default interval
  await startIntervalStream(DEFAULT_CONFIG.interval);

  io.on("connection", (socket) => {
    console.log(`User connected: ${socket.id}`);

    // Set default configuration for new user
    userConfigs.set(socket.id, { ...DEFAULT_CONFIG });

    // Send default configuration
    socket.emit("config", DEFAULT_CONFIG);

    // Handle user configuration updates
    socket.on("updateConfig", async (newConfig) => {
      console.log(`Config update from ${socket.id}:`, newConfig);

      // Validate and merge configuration
      const config = {
        interval: newConfig.interval || DEFAULT_CONFIG.interval,
        threshold: Math.max(0.1, Math.min(10, newConfig.threshold || DEFAULT_CONFIG.threshold)),
        volumeMultiplier: Math.max(1, Math.min(10, newConfig.volumeMultiplier || DEFAULT_CONFIG.volumeMultiplier)),
        rsiPeriod: DEFAULT_CONFIG.rsiPeriod
      };

      userConfigs.set(socket.id, config);

      // Update active streams
      await updateActiveStreams();

      // Confirm configuration update
      socket.emit("configUpdated", config);

      // Clear existing data for this user
      socket.emit("clearData");
    });

    // Handle disconnection
    socket.on("disconnect", async () => {
      console.log(`User disconnected: ${socket.id}`);
      userConfigs.delete(socket.id);

      // Update active streams (might stop unused intervals)
      await updateActiveStreams();
    });
  });

  server.listen(3000, () => console.log("http://localhost:3000"));
})();
