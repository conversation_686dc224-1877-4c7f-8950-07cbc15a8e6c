<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Realtime Green Coins</title>
  <script src="/socket.io/socket.io.js"></script>
  <link rel="stylesheet" href="css/style.css">
</head>
<body>
  <h1>Realtime Green Coins</h1>
  <div id="subtitle" class="subtitle"></div

  <!-- Configuration Panel -->
  <div class="config-panel">
    <div class="config-group">
      <label for="interval-select">
        Interval
        <span class="info-icon" data-tooltip="Time frame for price analysis. Shorter intervals (1m, 3m) show more frequent signals but may be noisier. Longer intervals (1h, 4h) show more reliable trends but fewer signals.">
          <svg fill="#ffffff" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
            width="14px" height="14px" viewBox="0 0 416.979 416.979"
            xml:space="preserve">
            <g>
              <path d="M356.004,61.156c-81.37-81.47-213.377-81.551-294.848-0.182c-81.47,81.371-81.552,213.379-0.181,294.85
                c81.369,81.47,213.378,81.551,294.849,0.181C437.293,274.636,437.375,142.626,356.004,61.156z M237.6,340.786
                c0,3.217-2.607,5.822-5.822,5.822h-46.576c-3.215,0-5.822-2.605-5.822-5.822V167.885c0-3.217,2.607-5.822,5.822-5.822h46.576
                c3.215,0,5.822,2.604,5.822,5.822V340.786z M208.49,137.901c-18.618,0-33.766-15.146-33.766-33.765
                c0-18.617,15.147-33.766,33.766-33.766c18.619,0,33.766,15.148,33.766,33.766C242.256,122.755,227.107,137.901,208.49,137.901z"/>
            </g>
          </svg>
        </span>
      </label>
      <select id="interval-select">
        <option value="1m">1 minute</option>
        <option value="3m">3 minutes</option>
        <option value="5m" selected>5 minutes</option>
        <option value="15m">15 minutes</option>
        <option value="30m">30 minutes</option>
        <option value="1h">1 hour</option>
        <option value="4h">4 hours</option>
      </select>
    </div>

    <div class="config-group">
      <label for="threshold-input">
        Threshold (%)
        <span class="info-icon" data-tooltip="Minimum price increase percentage to show a coin. Higher values (2-5%) show only strong moves, lower values (0.1-1%) show more coins but with smaller gains.">
          <svg fill="#ffffff" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
            width="14px" height="14px" viewBox="0 0 416.979 416.979"
            xml:space="preserve">
            <g>
              <path d="M356.004,61.156c-81.37-81.47-213.377-81.551-294.848-0.182c-81.47,81.371-81.552,213.379-0.181,294.85
                c81.369,81.47,213.378,81.551,294.849,0.181C437.293,274.636,437.375,142.626,356.004,61.156z M237.6,340.786
                c0,3.217-2.607,5.822-5.822,5.822h-46.576c-3.215,0-5.822-2.605-5.822-5.822V167.885c0-3.217,2.607-5.822,5.822-5.822h46.576
                c3.215,0,5.822,2.604,5.822,5.822V340.786z M208.49,137.901c-18.618,0-33.766-15.146-33.766-33.765
                c0-18.617,15.147-33.766,33.766-33.766c18.619,0,33.766,15.148,33.766,33.766C242.256,122.755,227.107,137.901,208.49,137.901z"/>
            </g>
          </svg>
        </span>
      </label>
      <input type="number" id="threshold-input" min="0.1" max="10" step="0.1" value="0.5">
    </div>

    <div class="config-group">
      <label for="volume-multiplier-input">
        Volume Spike (x)
        <span class="info-icon" data-tooltip="Shows coins with volume X times higher than average. Higher values (5-10x) show only exceptional volume spikes, lower values (1-3x) show more coins with moderate volume increases.">
          <svg fill="#ffffff" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
            width="14px" height="14px" viewBox="0 0 416.979 416.979"
            xml:space="preserve">
            <g>
              <path d="M356.004,61.156c-81.37-81.47-213.377-81.551-294.848-0.182c-81.47,81.371-81.552,213.379-0.181,294.85
                c81.369,81.47,213.378,81.551,294.849,0.181C437.293,274.636,437.375,142.626,356.004,61.156z M237.6,340.786
                c0,3.217-2.607,5.822-5.822,5.822h-46.576c-3.215,0-5.822-2.605-5.822-5.822V167.885c0-3.217,2.607-5.822,5.822-5.822h46.576
                c3.215,0,5.822,2.604,5.822,5.822V340.786z M208.49,137.901c-18.618,0-33.766-15.146-33.766-33.765
                c0-18.617,15.147-33.766,33.766-33.766c18.619,0,33.766,15.148,33.766,33.766C242.256,122.755,227.107,137.901,208.49,137.901z"/>
            </g>
          </svg>
        </span>
      </label>
      <input type="number" id="volume-multiplier-input" min="1" max="10" step="0.5" value="3">
    </div>

    <div class="config-group">
      <label for="rsi-period-input">
        RSI Period
        <span class="info-icon" data-tooltip="Number of periods for RSI calculation. Standard is 14. Lower values (7-10) make RSI more sensitive to price changes, higher values (20-25) make it smoother but less responsive.">
          <svg fill="#ffffff" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
            width="14px" height="14px" viewBox="0 0 416.979 416.979"
            xml:space="preserve">
            <g>
              <path d="M356.004,61.156c-81.37-81.47-213.377-81.551-294.848-0.182c-81.47,81.371-81.552,213.379-0.181,294.85
                c81.369,81.47,213.378,81.551,294.849,0.181C437.293,274.636,437.375,142.626,356.004,61.156z M237.6,340.786
                c0,3.217-2.607,5.822-5.822,5.822h-46.576c-3.215,0-5.822-2.605-5.822-5.822V167.885c0-3.217,2.607-5.822,5.822-5.822h46.576
                c3.215,0,5.822,2.604,5.822,5.822V340.786z M208.49,137.901c-18.618,0-33.766-15.146-33.766-33.765
                c0-18.617,15.147-33.766,33.766-33.766c18.619,0,33.766,15.148,33.766,33.766C242.256,122.755,227.107,137.901,208.49,137.901z"/>
            </g>
          </svg>
        </span>
      </label>
      <input type="number" id="rsi-period-input" min="5" max="50" step="1" value="14">
    </div>

    <button class="apply-btn" id="apply-config">Apply Settings</button>

    <div class="config-status" id="config-status">
      Ready to apply settings
    </div>
  </div>

  <div class="coins-container" id="coins"></div>

  <script>
    const socket = io();
    const container = document.getElementById("coins");
    const subtitle = document.getElementById("subtitle");
    const cards = {};

    // Configuration elements
    const intervalSelect = document.getElementById("interval-select");
    const thresholdInput = document.getElementById("threshold-input");
    const volumeMultiplierInput = document.getElementById("volume-multiplier-input");
    const rsiPeriodInput = document.getElementById("rsi-period-input");
    const applyBtn = document.getElementById("apply-config");
    const configStatus = document.getElementById("config-status");

    // Current configuration
    let currentConfig = {
      interval: "5m",
      threshold: 0.5,
      volumeMultiplier: 3,
      rsiPeriod: 14
    };

    // ✅ Load configuration from localStorage
    function loadConfigFromStorage() {
      const saved = localStorage.getItem("cryptoConfig");
      if (saved) {
        try {
          const config = JSON.parse(saved);
          currentConfig = { ...currentConfig, ...config };

          // Update UI elements
          intervalSelect.value = currentConfig.interval;
          thresholdInput.value = currentConfig.threshold;
          volumeMultiplierInput.value = currentConfig.volumeMultiplier;
          rsiPeriodInput.value = currentConfig.rsiPeriod;

          return config;
        } catch (e) {
          console.error("Error loading config from localStorage:", e);
        }
      }
      return null;
    }

    // ✅ Save configuration to localStorage
    function saveConfigToStorage(config) {
      try {
        localStorage.setItem("cryptoConfig", JSON.stringify(config));
      } catch (e) {
        console.error("Error saving config to localStorage:", e);
      }
    }

    // ✅ Update subtitle with current configuration
    function updateSubtitle() {
      subtitle.textContent = `Interval: ${currentConfig.interval} | Threshold: ${currentConfig.threshold}% | Volume Spike: ${currentConfig.volumeMultiplier}x | RSI Period: ${currentConfig.rsiPeriod}`;
    }

    // ✅ Apply configuration
    function applyConfiguration() {
      const newConfig = {
        interval: intervalSelect.value,
        threshold: parseFloat(thresholdInput.value),
        volumeMultiplier: parseFloat(volumeMultiplierInput.value),
        rsiPeriod: parseInt(rsiPeriodInput.value)
      };

      // Validate inputs
      if (newConfig.threshold < 0.1 || newConfig.threshold > 10) {
        configStatus.textContent = "Threshold must be between 0.1% and 10%";
        configStatus.style.color = "#ff4444";
        return;
      }

      if (newConfig.volumeMultiplier < 1 || newConfig.volumeMultiplier > 10) {
        configStatus.textContent = "Volume multiplier must be between 1x and 10x";
        configStatus.style.color = "#ff4444";
        return;
      }

      if (newConfig.rsiPeriod < 5 || newConfig.rsiPeriod > 50) {
        configStatus.textContent = "RSI period must be between 5 and 50";
        configStatus.style.color = "#ff4444";
        return;
      }

      currentConfig = newConfig;
      saveConfigToStorage(currentConfig);
      updateSubtitle();

      // Clear existing cards
      Object.keys(cards).forEach(symbol => {
        removeCard(symbol);
      });

      // Send configuration to server
      socket.emit("updateConfig", currentConfig);

      configStatus.textContent = "Settings applied successfully!";
      configStatus.style.color = "#00ff00";

      setTimeout(() => {
        configStatus.textContent = "Ready to apply settings";
        configStatus.style.color = "#888";
      }, 3000);
    }

    // 🟢 Handle server configuration updates
    socket.on("config", (data) => {
      currentConfig = { ...currentConfig, ...data };
      updateSubtitle();
    });

    socket.on("configUpdated", (data) => {
      currentConfig = { ...currentConfig, ...data };
      updateSubtitle();
    });

    socket.on("clearData", () => {
      Object.keys(cards).forEach(symbol => {
        removeCard(symbol);
      });
    });

    function getRSIClass(rsi) {
      if (rsi <= 30) return 'oversold';
      if (rsi >= 70) return 'overbought';
      return 'neutral';
    }

    function formatVolume(volume) {
      if (volume >= 1e9) return (volume / 1e9).toFixed(1) + 'B';
      if (volume >= 1e6) return (volume / 1e6).toFixed(1) + 'M';
      if (volume >= 1e3) return (volume / 1e3).toFixed(1) + 'K';
      return volume.toFixed(0);
    }

    function addCard(symbol, change, spike, rsi, volume, price, volumeData) {
      const quote = "USDT";
      const base = symbol.replace(quote, "");
      const url = `https://www.binance.com/en/trade/${base}_${quote}`;

      const card = document.createElement("div");
      card.className = "coin-card";

      const link = document.createElement("a");
      link.href = url;
      link.target = "_blank";
      link.textContent = `${base}/${quote}`;

      const changeSpan = document.createElement("span");
      changeSpan.className = "change green";
      changeSpan.textContent = change + "%";

      const priceSpan = document.createElement("span");
      priceSpan.className = "price";
      priceSpan.textContent = `$${price}`;

      card.appendChild(link);
      card.appendChild(changeSpan);
      card.appendChild(priceSpan);

      // RSI display
      if (rsi !== null && rsi !== undefined) {
        const rsiSpan = document.createElement("span");
        rsiSpan.className = `rsi ${getRSIClass(rsi)}`;
        rsiSpan.textContent = `RSI: ${rsi}`;
        card.appendChild(rsiSpan);
      }

      // Volume spike
      if (spike) {
        const spikeTag = document.createElement("span");
        spikeTag.className = "spike";
        spikeTag.textContent = "🔥 Volume Spike!";
        card.appendChild(spikeTag);
      }

      // Volume container
      const volumeContainer = document.createElement("div");
      volumeContainer.className = "volume-container";

      const volumeLabel = document.createElement("div");
      volumeLabel.className = "volume-label";
      volumeLabel.textContent = "Volume";

      const volumeBar = document.createElement("div");
      volumeBar.className = "volume-bar";

      const volumeFill = document.createElement("div");
      volumeFill.className = `volume-fill ${spike ? 'spike' : 'normal'}`;

      const volumeText = document.createElement("div");
      volumeText.className = "volume-text";

      if (volumeData) {
        const percentage = Math.min((volumeData.ratio / 5) * 100, 100); // Scale to max 5x
        volumeFill.style.width = percentage + "%";
        volumeText.textContent = `${formatVolume(volumeData.current)} (${volumeData.ratio}x avg)`;
      } else {
        volumeFill.style.width = "50%";
        volumeText.textContent = formatVolume(volume);
      }

      volumeBar.appendChild(volumeFill);
      volumeContainer.appendChild(volumeLabel);
      volumeContainer.appendChild(volumeBar);
      volumeContainer.appendChild(volumeText);
      card.appendChild(volumeContainer);

      container.appendChild(card);
      cards[symbol] = { card, changeSpan, priceSpan, volumeFill, volumeText };
    }

    function updateCard(symbol, change, spike, rsi, volume, price, volumeData) {
      const { card, changeSpan, priceSpan, volumeFill, volumeText } = cards[symbol];
      changeSpan.textContent = change + "%";
      if (priceSpan) priceSpan.textContent = `$${price}`;

      // Update RSI
      let rsiSpan = card.querySelector(".rsi");
      if (rsi !== null && rsi !== undefined) {
        if (!rsiSpan) {
          rsiSpan = document.createElement("span");
          rsiSpan.className = "rsi";
          card.insertBefore(rsiSpan, card.querySelector(".spike") || card.querySelector(".volume-container"));
        }
        rsiSpan.className = `rsi ${getRSIClass(rsi)}`;
        rsiSpan.textContent = `RSI: ${rsi}`;
      } else if (rsiSpan) {
        rsiSpan.remove();
      }

      // Update volume spike
      let spikeTag = card.querySelector(".spike");
      if (spike && !spikeTag) {
        spikeTag = document.createElement("span");
        spikeTag.className = "spike";
        spikeTag.textContent = "🔥 Volume Spike!";
        card.insertBefore(spikeTag, card.querySelector(".volume-container"));
      } else if (!spike && spikeTag) {
        spikeTag.remove();
      }

      // Update volume bar and text
      if (volumeFill) {
        volumeFill.className = `volume-fill ${spike ? 'spike' : 'normal'}`;

        if (volumeData) {
          const percentage = Math.min((volumeData.ratio / 5) * 100, 100);
          volumeFill.style.width = percentage + "%";
          if (volumeText) {
            volumeText.textContent = `${formatVolume(volumeData.current)} (${volumeData.ratio}x avg)`;
          }
        } else {
          volumeFill.style.width = spike ? "100%" : "50%";
          if (volumeText) {
            volumeText.textContent = formatVolume(volume);
          }
        }
      }
    }

    function removeCard(symbol) {
      if (cards[symbol]) {
        cards[symbol].card.remove();
        delete cards[symbol];
      }
    }

    // add/update green coin
    socket.on("green", (data) => {
      if (!cards[data.symbol]) {
        addCard(data.symbol, data.change, data.spike, data.rsi, data.volume, data.price, data.volumeData);
      } else {
        updateCard(data.symbol, data.change, data.spike, data.rsi, data.volume, data.price, data.volumeData);
      }
    });

    // remove if red
    socket.on("red", (data) => {
      removeCard(data.symbol);
    });

    // ✅ Event listeners
    applyBtn.addEventListener("click", applyConfiguration);

    // Apply settings on Enter key
    [thresholdInput, volumeMultiplierInput, rsiPeriodInput].forEach(input => {
      input.addEventListener("keypress", (e) => {
        if (e.key === "Enter") {
          applyConfiguration();
        }
      });
    });

    intervalSelect.addEventListener("change", () => {
      configStatus.textContent = "Click Apply Settings to update";
      configStatus.style.color = "#ffaa44";
    });

    [thresholdInput, volumeMultiplierInput, rsiPeriodInput].forEach(input => {
      input.addEventListener("input", () => {
        configStatus.textContent = "Click Apply Settings to update";
        configStatus.style.color = "#ffaa44";
      });
    });

    // ✅ Initialize on page load
    socket.on("connect", () => {
      console.log("Connected to server");

      // Load saved configuration or use defaults
      const savedConfig = loadConfigFromStorage();
      if (savedConfig) {
        // Send saved configuration to server
        socket.emit("updateConfig", currentConfig);
      }

      updateSubtitle();
    });

    // Load configuration on page load
    loadConfigFromStorage();
    updateSubtitle();
  </script>
</body>
</html>
